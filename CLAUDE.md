# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands
- **Build**: No build command found in the repository.
- **Lint**: No lint command found in the repository.
- **Test**: No test command found in the repository.

## High-Level Architecture
- The repository contains a simple HTML file (`网页开发助手.html`) showcasing an AI assistant's capabilities, including knowledge graph management, web content fetching, and smart search.
- The file `chunk_test1.txt` appears to be a test file for content segmentation.

## Notes
- No README.md or other configuration files were found to provide additional context.