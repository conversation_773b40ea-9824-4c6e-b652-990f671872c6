<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AI助手能力介绍页面，展示各项技术功能">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🤖</text></svg>">
    <title>AI助手能力介绍</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 关键CSS - 首屏内容样式 */
        .container, header, .feature-card {
            opacity: 1;
            animation: fadeIn 0.5s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .feature-card {
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-12">
        <header class="text-center mb-16">
            <h1 class="text-4xl font-bold text-indigo-700 mb-4">AI助手能力介绍</h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">我是一款多功能AI助手，可以帮助您完成各种任务</p>
        </header>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 知识图谱 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-md border border-gray-100">
                <div class="text-indigo-500 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">知识图谱管理</h3>
                <p class="text-gray-600">可以创建、查询和管理知识图谱中的实体和关系，帮助您组织和连接信息。</p>
            </div>

            <!-- 网页内容获取 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-md border border-gray-100">
                <div class="text-indigo-500 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">网页内容获取</h3>
                <p class="text-gray-600">可以从网页获取HTML、Markdown、纯文本或JSON格式的内容，方便您进行信息收集和分析。</p>
            </div>

            <!-- 智能搜索 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-md border border-gray-100">
                <div class="text-indigo-500 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">智能搜索</h3>
                <p class="text-gray-600">使用先进的AI搜索引擎，可以为您提供全面、实时的搜索结果，包括新闻、一般信息和特定领域内容。</p>
            </div>

            <!-- 文档查询 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-md border border-gray-100">
                <div class="text-indigo-500 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">技术文档查询</h3>
                <p class="text-gray-600">可以获取各种技术库和框架的最新文档，帮助开发者快速找到所需的技术参考。</p>
            </div>

            <!-- 代码执行 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-md border border-gray-100">
                <div class="text-indigo-500 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">代码执行</h3>
                <p class="text-gray-600">能够执行Python代码片段，帮助您进行计算、数据处理或验证算法。</p>
            </div>

            <!-- 网页开发 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-md border border-gray-100">
                <div class="text-indigo-500 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">网页开发</h3>
                <p class="text-gray-600">精通HTML、CSS、JavaScript和TailwindCSS，可以为您创建响应式、现代化的网页界面。</p>
            </div>
        </div>

        <div class="mt-16 bg-white rounded-xl p-8 shadow-md border border-gray-100 max-w-4xl mx-auto">
            <h2 class="text-2xl font-bold text-indigo-700 mb-4">如何使用我</h2>
            <p class="text-gray-600 mb-4">您可以直接向我提问或下达任务指令，我会根据需求选择合适的工具来完成任务。例如：</p>
            <ul class="list-disc pl-6 space-y-2 text-gray-600">
                <li>"搜索最新的React文档"</li>
                <li>"从https://example.com获取内容并总结"</li>
                <li>"创建一个包含三个卡片布局的响应式网页"</li>
                <li>"计算5的阶乘是多少"</li>
                <li>"在知识图谱中创建'人工智能'实体并与'机器学习'建立关系"</li>
            </ul>
        </div>
    </div>
</body>
</html>
